"""
نظام إدارة الألوان والثيمات للوضع الليلي والعادي
"""
import json
import os
from typing import Dict, Any

class ThemeManager:
    """مدير الألوان والثيمات"""
    
    def __init__(self):
        self.settings_file = "theme_settings.json"
        self.current_theme = "light"  # دائماً الوضع الفاتح
        # لا نحمل تفضيلات الثيم - نبقى في الوضع الفاتح
    
    def get_colors(self) -> Dict[str, str]:
        """الحصول على ألوان الثيم الحالي"""
        if self.current_theme == "dark":
            return self.get_dark_colors()
        else:
            return self.get_light_colors()
    
    def get_light_colors(self) -> Dict[str, str]:
        """ألوان الوضع العادي (الفاتح)"""
        return {
            # الخلفيات الرئيسية
            "primary_bg": "#FFFFFF",
            "secondary_bg": "#F8F9FA",
            "sidebar_bg": "qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 rgba(26, 32, 44, 0.95), stop:0.5 rgba(45, 55, 72, 0.95), stop:1 rgba(26, 32, 44, 0.95))",
            "card_bg": "#FFFFFF",
            "header_bg": "#FFFFFF",
            
            # النصوص
            "primary_text": "#212529",
            "secondary_text": "#6C757D",
            "sidebar_text": "#FFFFFF",
            "link_text": "#0D6EFD",
            
            # الحدود
            "border_color": "#DEE2E6",
            "border_light": "rgba(0, 0, 0, 0.1)",
            "border_dark": "rgba(0, 0, 0, 0.2)",
            
            # الأزرار
            "button_bg": "#FFFFFF",
            "button_hover": "#E9ECEF",
            "button_pressed": "#DEE2E6",
            "button_text": "#212529",
            
            # الألوان الوظيفية
            "success": "#28A745",
            "warning": "#FFC107",
            "danger": "#DC3545",
            "info": "#17A2B8",
            "primary": "#007BFF",
            
            # الجداول
            "table_header": "#F8F9FA",
            "table_row_even": "#FFFFFF",
            "table_row_odd": "#F8F9FA",
            "table_border": "#DEE2E6",
            
            # الإدخال
            "input_bg": "#FFFFFF",
            "input_border": "#CED4DA",
            "input_focus": "#80BDFF",
            "input_text": "#495057",
            
            # الظلال
            "shadow_light": "rgba(0, 0, 0, 0.1)",
            "shadow_medium": "rgba(0, 0, 0, 0.15)",
            "shadow_dark": "rgba(0, 0, 0, 0.2)",
        }
    
    def get_dark_colors(self) -> Dict[str, str]:
        """ألوان الوضع الليلي (المظلم)"""
        return {
            # الخلفيات الرئيسية
            "primary_bg": "#1A1A1A",
            "secondary_bg": "#2D2D2D",
            "sidebar_bg": "qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 rgba(15, 15, 15, 0.95), stop:0.5 rgba(25, 25, 25, 0.95), stop:1 rgba(15, 15, 15, 0.95))",
            "card_bg": "#2D2D2D",
            "header_bg": "#2D2D2D",
            
            # النصوص
            "primary_text": "#FFFFFF",
            "secondary_text": "#B0B0B0",
            "sidebar_text": "#FFFFFF",
            "link_text": "#4A9EFF",
            
            # الحدود
            "border_color": "#404040",
            "border_light": "rgba(255, 255, 255, 0.1)",
            "border_dark": "rgba(255, 255, 255, 0.2)",
            
            # الأزرار
            "button_bg": "#3D3D3D",
            "button_hover": "#4D4D4D",
            "button_pressed": "#5D5D5D",
            "button_text": "#FFFFFF",
            
            # الألوان الوظيفية
            "success": "#4CAF50",
            "warning": "#FF9800",
            "danger": "#F44336",
            "info": "#2196F3",
            "primary": "#2196F3",
            
            # الجداول
            "table_header": "#3D3D3D",
            "table_row_even": "#2D2D2D",
            "table_row_odd": "#353535",
            "table_border": "#404040",
            
            # الإدخال
            "input_bg": "#3D3D3D",
            "input_border": "#555555",
            "input_focus": "#4A9EFF",
            "input_text": "#FFFFFF",
            
            # الظلال
            "shadow_light": "rgba(0, 0, 0, 0.3)",
            "shadow_medium": "rgba(0, 0, 0, 0.5)",
            "shadow_dark": "rgba(0, 0, 0, 0.7)",
        }
    
    def get_stylesheet(self, widget_type: str = "general") -> str:
        """الحصول على stylesheet للعنصر المحدد"""
        colors = self.get_colors()
        
        if widget_type == "general":
            return self._get_general_stylesheet(colors)
        elif widget_type == "sidebar":
            return self._get_sidebar_stylesheet(colors)
        elif widget_type == "table":
            return self._get_table_stylesheet(colors)
        elif widget_type == "form":
            return self._get_form_stylesheet(colors)
        elif widget_type == "button":
            return self._get_button_stylesheet(colors)
        else:
            return self._get_general_stylesheet(colors)
    
    def _get_general_stylesheet(self, colors: Dict[str, str]) -> str:
        """الأنماط العامة"""
        return f"""
            QWidget {{
                background-color: {colors['primary_bg']};
                color: {colors['primary_text']};
                font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            }}
            
            QFrame {{
                background-color: {colors['card_bg']};
                border: 1px solid {colors['border_color']};
                border-radius: 8px;
            }}
            
            QLabel {{
                color: {colors['primary_text']};
                background-color: transparent;
            }}
        """
    
    def _get_sidebar_stylesheet(self, colors: Dict[str, str]) -> str:
        """أنماط الشريط الجانبي"""
        return f"""
            QFrame#slideSidebar {{
                background: {colors['sidebar_bg']};
                border: none;
                border-right: 1px solid {colors['border_light']};
            }}
            
            QLabel {{
                color: {colors['sidebar_text']};
            }}
        """
    
    def _get_table_stylesheet(self, colors: Dict[str, str]) -> str:
        """أنماط الجداول"""
        return f"""
            QTableWidget {{
                background-color: {colors['primary_bg']};
                alternate-background-color: {colors['table_row_odd']};
                gridline-color: {colors['table_border']};
                color: {colors['primary_text']};
                border: 1px solid {colors['border_color']};
                border-radius: 8px;
            }}
            
            QTableWidget::item {{
                padding: 8px;
                border-bottom: 1px solid {colors['table_border']};
            }}
            
            QTableWidget::item:selected {{
                background-color: {colors['primary']};
                color: white;
            }}
            
            QHeaderView::section {{
                background-color: {colors['table_header']};
                color: {colors['primary_text']};
                padding: 8px;
                border: 1px solid {colors['table_border']};
                font-weight: bold;
            }}
        """
    
    def _get_form_stylesheet(self, colors: Dict[str, str]) -> str:
        """أنماط النماذج"""
        return f"""
            QLineEdit, QTextEdit, QComboBox, QSpinBox, QDoubleSpinBox {{
                background-color: {colors['input_bg']};
                border: 2px solid {colors['input_border']};
                border-radius: 6px;
                padding: 8px;
                color: {colors['input_text']};
                font-size: 14px;
            }}
            
            QLineEdit:focus, QTextEdit:focus, QComboBox:focus {{
                border-color: {colors['input_focus']};
            }}
            
            QComboBox::drop-down {{
                border: none;
                background-color: {colors['input_bg']};
            }}
            
            QComboBox::down-arrow {{
                image: none;
                border-left: 5px solid transparent;
                border-right: 5px solid transparent;
                border-top: 5px solid {colors['primary_text']};
            }}
        """
    
    def _get_button_stylesheet(self, colors: Dict[str, str]) -> str:
        """أنماط الأزرار"""
        return f"""
            QPushButton {{
                background-color: {colors['button_bg']};
                border: 2px solid {colors['border_color']};
                border-radius: 8px;
                padding: 10px 20px;
                color: {colors['button_text']};
                font-size: 14px;
                font-weight: 600;
            }}
            
            QPushButton:hover {{
                background-color: {colors['button_hover']};
                border-color: {colors['primary']};
            }}
            
            QPushButton:pressed {{
                background-color: {colors['button_pressed']};
            }}
            
            QPushButton:disabled {{
                background-color: {colors['secondary_bg']};
                color: {colors['secondary_text']};
                border-color: {colors['border_color']};
            }}
        """
    
    def toggle_theme(self):
        """تبديل الثيم - معطل (دائماً الوضع الفاتح)"""
        # لا نفعل شيء - نبقى في الوضع الفاتح
        pass

    def set_theme(self, theme: str):
        """تعيين ثيم محدد - معطل (دائماً الوضع الفاتح)"""
        # لا نفعل شيء - نبقى في الوضع الفاتح
        pass
    
    def is_dark_mode(self) -> bool:
        """التحقق من الوضع الليلي"""
        return self.current_theme == "dark"
    
    def save_theme_preference(self):
        """حفظ تفضيل الثيم"""
        try:
            settings = {"theme": self.current_theme}
            with open(self.settings_file, 'w', encoding='utf-8') as f:
                json.dump(settings, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"Error saving theme preference: {e}")
    
    def load_theme_preference(self):
        """تحميل تفضيل الثيم - مجبر على الوضع الفاتح دائماً"""
        # إجبار النظام على استخدام الثيم الفاتح دائماً
        self.current_theme = 'light'
        print("🎨 تم تطبيق الثيم الفاتح (مجبر)")

        # محاولة حفظ الإعداد للتأكد
        try:
            settings = {'theme': 'light'}
            with open(self.settings_file, 'w', encoding='utf-8') as f:
                json.dump(settings, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"تحذير: لا يمكن حفظ إعدادات الثيم: {e}")

# إنشاء مثيل عام لمدير الثيمات
theme_manager = ThemeManager()
