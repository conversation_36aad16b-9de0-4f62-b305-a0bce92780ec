#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
برنامج مولد أكواد التفعيل للمطور
License Code Generator for Developer
"""

import tkinter as tk
from tkinter import messagebox, ttk
from datetime import datetime, timedelta
import hashlib
import os
import json

class LicenseCodeGenerator:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("🔧 مولد أكواد التفعيل - للمطور فقط")
        self.root.geometry("800x900")
        self.root.resizable(True, True)
        self.root.configure(bg='#2c3e50')
        
        # ملف حفظ الأكواد المُولدة
        self.generated_codes_file = "generated_codes.json"
        self.load_generated_codes()
        
        self.setup_ui()
        
    def load_generated_codes(self):
        """تحميل الأكواد المُولدة مسبقاً"""
        try:
            if os.path.exists(self.generated_codes_file):
                with open(self.generated_codes_file, 'r', encoding='utf-8') as f:
                    self.generated_codes = json.load(f)
            else:
                self.generated_codes = []
        except:
            self.generated_codes = []
    
    def save_generated_codes(self):
        """حفظ الأكواد المُولدة"""
        try:
            with open(self.generated_codes_file, 'w', encoding='utf-8') as f:
                json.dump(self.generated_codes, f, ensure_ascii=False, indent=2)
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في حفظ الأكواد: {e}")
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # إنشاء إطار تمرير
        canvas = tk.Canvas(self.root, bg='#2c3e50')
        scrollbar = tk.Scrollbar(self.root, orient="vertical", command=canvas.yview)
        scrollable_frame = tk.Frame(canvas, bg='#2c3e50')

        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )

        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

        # العنوان الرئيسي
        title_frame = tk.Frame(scrollable_frame, bg='#2c3e50')
        title_frame.pack(pady=20)

        icon_label = tk.Label(
            title_frame,
            text="🔧",
            font=("Arial", 48),
            bg='#2c3e50',
            fg='#e74c3c'
        )
        icon_label.pack()

        title_label = tk.Label(
            title_frame,
            text="مولد أكواد التفعيل",
            font=("Arial", 24, "bold"),
            bg='#2c3e50',
            fg='#ecf0f1'
        )
        title_label.pack()

        subtitle_label = tk.Label(
            title_frame,
            text="للمطور فقط - Developer Only",
            font=("Arial", 14),
            bg='#2c3e50',
            fg='#95a5a6'
        )
        subtitle_label.pack(pady=5)

        # إطار إدخال بيانات العميل
        input_frame = tk.LabelFrame(
            scrollable_frame,
            text="📋 بيانات العميل",
            font=("Arial", 16, "bold"),
            bg='#34495e',
            fg='#ecf0f1',
            padx=20,
            pady=15
        )
        input_frame.pack(pady=20, padx=40, fill='x')

        # كود العميل
        customer_frame = tk.Frame(input_frame, bg='#34495e')
        customer_frame.pack(fill='x', pady=10)

        tk.Label(
            customer_frame,
            text="🔑 كود العميل:",
            font=("Arial", 14, "bold"),
            bg='#34495e',
            fg='#ecf0f1'
        ).pack(side='left')

        self.customer_code_entry = tk.Entry(
            customer_frame,
            font=("Courier", 14, "bold"),
            width=15,
            justify='center',
            bg='#ecf0f1',
            fg='#2c3e50'
        )
        self.customer_code_entry.pack(side='right', padx=10)

        # رقم الجهاز
        machine_frame = tk.Frame(input_frame, bg='#34495e')
        machine_frame.pack(fill='x', pady=10)

        tk.Label(
            machine_frame,
            text="💻 رقم الجهاز:",
            font=("Arial", 14, "bold"),
            bg='#34495e',
            fg='#ecf0f1'
        ).pack(side='left')

        self.machine_id_entry = tk.Entry(
            machine_frame,
            font=("Courier", 14, "bold"),
            width=20,
            justify='center',
            bg='#ecf0f1',
            fg='#2c3e50'
        )
        self.machine_id_entry.pack(side='right', padx=10)

        # مدة الترخيص
        duration_frame = tk.Frame(input_frame, bg='#34495e')
        duration_frame.pack(fill='x', pady=10)

        tk.Label(
            duration_frame,
            text="⏰ مدة الترخيص (أيام):",
            font=("Arial", 14, "bold"),
            bg='#34495e',
            fg='#ecf0f1'
        ).pack(side='left')

        self.duration_var = tk.StringVar(value="365")
        duration_combo = ttk.Combobox(
            duration_frame,
            textvariable=self.duration_var,
            values=["30", "90", "180", "365", "730", "1095"],
            font=("Arial", 12),
            width=10,
            justify='center'
        )
        duration_combo.pack(side='right', padx=10)

        # اسم العميل (اختياري)
        name_frame = tk.Frame(input_frame, bg='#34495e')
        name_frame.pack(fill='x', pady=10)

        tk.Label(
            name_frame,
            text="👤 اسم العميل (اختياري):",
            font=("Arial", 14, "bold"),
            bg='#34495e',
            fg='#ecf0f1'
        ).pack(side='left')

        self.customer_name_entry = tk.Entry(
            name_frame,
            font=("Arial", 12),
            width=25,
            bg='#ecf0f1',
            fg='#2c3e50'
        )
        self.customer_name_entry.pack(side='right', padx=10)

        # أزرار التحكم
        buttons_frame = tk.Frame(scrollable_frame, bg='#2c3e50')
        buttons_frame.pack(pady=30)

        # زر توليد الكود
        generate_btn = tk.Button(
            buttons_frame,
            text="🔧 توليد كود التفعيل",
            font=("Arial", 16, "bold"),
            bg='#27ae60',
            fg='white',
            padx=30,
            pady=15,
            command=self.generate_license_code
        )
        generate_btn.pack(side='left', padx=15)

        # زر مسح الحقول
        clear_btn = tk.Button(
            buttons_frame,
            text="🗑️ مسح الحقول",
            font=("Arial", 14),
            bg='#e74c3c',
            fg='white',
            padx=20,
            pady=12,
            command=self.clear_fields
        )
        clear_btn.pack(side='left', padx=15)

        # زر عرض الأكواد المُولدة
        history_btn = tk.Button(
            buttons_frame,
            text="📋 عرض السجل",
            font=("Arial", 14),
            bg='#3498db',
            fg='white',
            padx=20,
            pady=12,
            command=self.show_history
        )
        history_btn.pack(side='left', padx=15)

        # إطار عرض النتيجة
        result_frame = tk.LabelFrame(
            scrollable_frame,
            text="🎯 كود التفعيل المُولد",
            font=("Arial", 16, "bold"),
            bg='#34495e',
            fg='#ecf0f1',
            padx=20,
            pady=15
        )
        result_frame.pack(pady=20, padx=40, fill='x')

        # حقل عرض الكود
        self.result_text = tk.Text(
            result_frame,
            height=8,
            font=("Courier", 12),
            bg='#ecf0f1',
            fg='#2c3e50',
            wrap=tk.WORD,
            relief='sunken',
            bd=2
        )
        self.result_text.pack(fill='x', pady=10)

        # زر نسخ الكود
        copy_btn = tk.Button(
            result_frame,
            text="📋 نسخ الكود",
            font=("Arial", 14, "bold"),
            bg='#f39c12',
            fg='white',
            padx=25,
            pady=10,
            command=self.copy_result
        )
        copy_btn.pack(pady=10)

        # إضافة مساحة إضافية
        spacer = tk.Frame(scrollable_frame, bg='#2c3e50', height=50)
        spacer.pack()

        # ربط عجلة الماوس بالتمرير
        def _on_mousewheel(event):
            canvas.yview_scroll(int(-1*(event.delta/120)), "units")
        canvas.bind_all("<MouseWheel>", _on_mousewheel)

    def generate_license_code(self):
        """توليد كود التفعيل"""
        try:
            # التحقق من البيانات المطلوبة
            customer_code = self.customer_code_entry.get().strip().upper()
            machine_id = self.machine_id_entry.get().strip().upper()
            duration_days = int(self.duration_var.get())
            customer_name = self.customer_name_entry.get().strip()

            if not customer_code or not machine_id:
                messagebox.showerror("خطأ", "يرجى إدخال كود العميل ورقم الجهاز")
                return

            if len(customer_code) < 4 or len(machine_id) < 4:
                messagebox.showerror("خطأ", "كود العميل ورقم الجهاز يجب أن يكونا 4 أحرف على الأقل")
                return

            # حساب تاريخ انتهاء الصلاحية
            expiry_date = datetime.now() + timedelta(days=duration_days)
            date_str = expiry_date.strftime("%Y%m%d")

            # إنشاء الهاش
            data_to_hash = f"{date_str}-{customer_code}-{machine_id}-SICOO2024"
            hash_code = hashlib.sha256(data_to_hash.encode()).hexdigest()[:8].upper()

            # تكوين كود التفعيل
            license_code = f"SICOO-{date_str}-{customer_code[:4]}-{machine_id[:4]}-{hash_code}"

            # إعداد النتيجة
            result = f"""✅ تم توليد كود التفعيل بنجاح!

🔑 كود التفعيل:
{license_code}

📊 تفاصيل الترخيص:
👤 العميل: {customer_name if customer_name else 'غير محدد'}
🔑 كود العميل: {customer_code}
💻 رقم الجهاز: {machine_id}
📅 صالح حتى: {expiry_date.strftime('%d/%m/%Y')}
⏰ المدة: {duration_days} يوم
🕒 تاريخ الإنشاء: {datetime.now().strftime('%d/%m/%Y %H:%M')}

📧 أرسل هذا الكود للعميل لتفعيل البرنامج"""

            # عرض النتيجة
            self.result_text.delete(1.0, tk.END)
            self.result_text.insert(1.0, result)

            # حفظ في السجل
            code_record = {
                "license_code": license_code,
                "customer_name": customer_name,
                "customer_code": customer_code,
                "machine_id": machine_id,
                "duration_days": duration_days,
                "expiry_date": expiry_date.isoformat(),
                "generated_date": datetime.now().isoformat()
            }
            
            self.generated_codes.append(code_record)
            self.save_generated_codes()

            messagebox.showinfo("نجح", "تم توليد كود التفعيل وحفظه في السجل!")

        except ValueError:
            messagebox.showerror("خطأ", "يرجى إدخال رقم صحيح لمدة الترخيص")
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء توليد الكود: {str(e)}")

    def clear_fields(self):
        """مسح جميع الحقول"""
        self.customer_code_entry.delete(0, tk.END)
        self.machine_id_entry.delete(0, tk.END)
        self.customer_name_entry.delete(0, tk.END)
        self.duration_var.set("365")
        self.result_text.delete(1.0, tk.END)

    def copy_result(self):
        """نسخ النتيجة إلى الحافظة"""
        result = self.result_text.get(1.0, tk.END).strip()
        if result:
            self.root.clipboard_clear()
            self.root.clipboard_append(result)
            messagebox.showinfo("تم النسخ", "تم نسخ النتيجة إلى الحافظة")
        else:
            messagebox.showwarning("تنبيه", "لا توجد نتيجة لنسخها")

    def show_history(self):
        """عرض سجل الأكواد المُولدة"""
        if not self.generated_codes:
            messagebox.showinfo("السجل فارغ", "لم يتم توليد أي أكواد بعد")
            return

        # إنشاء نافذة السجل
        history_window = tk.Toplevel(self.root)
        history_window.title("📋 سجل الأكواد المُولدة")
        history_window.geometry("900x600")
        history_window.configure(bg='#2c3e50')

        # إنشاء جدول
        columns = ("الكود", "العميل", "كود العميل", "رقم الجهاز", "المدة", "تاريخ الانتهاء", "تاريخ الإنشاء")
        tree = ttk.Treeview(history_window, columns=columns, show='headings', height=20)

        # تعيين عناوين الأعمدة
        for col in columns:
            tree.heading(col, text=col)
            tree.column(col, width=120)

        # إضافة البيانات
        for record in reversed(self.generated_codes):  # الأحدث أولاً
            expiry_date = datetime.fromisoformat(record['expiry_date']).strftime('%d/%m/%Y')
            generated_date = datetime.fromisoformat(record['generated_date']).strftime('%d/%m/%Y %H:%M')
            
            tree.insert('', 'end', values=(
                record['license_code'],
                record.get('customer_name', 'غير محدد'),
                record['customer_code'],
                record['machine_id'],
                f"{record['duration_days']} يوم",
                expiry_date,
                generated_date
            ))

        tree.pack(fill='both', expand=True, padx=10, pady=10)

        # زر إغلاق
        close_btn = tk.Button(
            history_window,
            text="❌ إغلاق",
            font=("Arial", 12),
            bg='#e74c3c',
            fg='white',
            padx=20,
            pady=8,
            command=history_window.destroy
        )
        close_btn.pack(pady=10)

    def run(self):
        """تشغيل التطبيق"""
        self.root.mainloop()

if __name__ == "__main__":
    app = LicenseCodeGenerator()
    app.run()
