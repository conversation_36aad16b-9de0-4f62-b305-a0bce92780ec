#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار إصلاح نافذة التفعيل مع حقل إدخال واضح
"""

import sys
from PyQt5.QtWidgets import (QApplication, QDialog, QVBoxLayout, QHBoxLayout, 
                            QLabel, QLineEdit, QPushButton, QFrame, QMessageBox)
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont
from license_manager import LicenseManager

class FixedActivationDialog(QDialog):
    """نافذة تفعيل محسنة مع حقل إدخال واضح"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("🔑 تفعيل البرنامج")
        self.setFixedSize(800, 700)
        self.setModal(True)
        
        # إنشاء مدير التراخيص
        self.license_manager = LicenseManager()
        
        # إعداد واجهة المستخدم
        self.setup_ui()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        layout = QVBoxLayout()
        layout.setSpacing(20)
        layout.setContentsMargins(40, 40, 40, 40)
        
        # العنوان الرئيسي
        title = QLabel("🔑 تفعيل البرنامج")
        title.setFont(QFont("Arial", 24, QFont.Bold))
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet("""
            QLabel {
                color: #e74c3c;
                background-color: #fdf2f2;
                padding: 20px;
                border-radius: 15px;
                border: 3px solid #e74c3c;
                margin-bottom: 20px;
            }
        """)
        layout.addWidget(title)
        
        # معلومات الجهاز
        info_frame = QFrame()
        info_frame.setStyleSheet("""
            QFrame {
                background-color: #f8f9fa;
                border: 3px solid #3498db;
                border-radius: 12px;
                padding: 25px;
                margin: 15px;
            }
        """)
        info_layout = QVBoxLayout()
        
        # الحصول على بيانات الجهاز
        customer_code = self.license_manager.get_customer_code()
        machine_id = self.license_manager.get_machine_id()
        
        # عنوان معلومات الجهاز
        info_title = QLabel("📋 بيانات الجهاز:")
        info_title.setFont(QFont("Arial", 16, QFont.Bold))
        info_title.setStyleSheet("color: #2c3e50; margin-bottom: 15px;")
        info_layout.addWidget(info_title)
        
        # كود العميل
        code_layout = QHBoxLayout()
        code_label = QLabel("🔑 كود العميل:")
        code_label.setFont(QFont("Arial", 14))
        code_layout.addWidget(code_label)
        
        code_value = QLabel(customer_code)
        code_value.setStyleSheet("font-family: monospace; color: #2980b9; font-weight: bold; font-size: 16px;")
        code_layout.addWidget(code_value)
        
        copy_code_btn = QPushButton("📋 نسخ")
        copy_code_btn.clicked.connect(lambda: self.copy_to_clipboard(customer_code))
        copy_code_btn.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                padding: 8px 15px;
                border-radius: 5px;
                font-size: 12px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)
        code_layout.addWidget(copy_code_btn)
        code_layout.addStretch()
        info_layout.addLayout(code_layout)
        
        # رقم الجهاز
        machine_layout = QHBoxLayout()
        machine_label = QLabel("💻 رقم الجهاز:")
        machine_label.setFont(QFont("Arial", 14))
        machine_layout.addWidget(machine_label)
        
        machine_value = QLabel(machine_id)
        machine_value.setStyleSheet("font-family: monospace; color: #2980b9; font-weight: bold; font-size: 16px;")
        machine_layout.addWidget(machine_value)
        
        copy_machine_btn = QPushButton("📋 نسخ")
        copy_machine_btn.clicked.connect(lambda: self.copy_to_clipboard(machine_id))
        copy_machine_btn.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                padding: 8px 15px;
                border-radius: 5px;
                font-size: 12px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)
        machine_layout.addWidget(copy_machine_btn)
        machine_layout.addStretch()
        info_layout.addLayout(machine_layout)
        
        info_frame.setLayout(info_layout)
        layout.addWidget(info_frame)
        
        # تعليمات
        instructions = QLabel("""
📞 للحصول على كود التفعيل:
1️⃣ أرسل البيانات أعلاه إلى المطور
2️⃣ البريد الإلكتروني: <EMAIL>
3️⃣ ستحصل على كود التفعيل خلال 24 ساعة
4️⃣ أدخل الكود في الحقل أدناه واضغط "تفعيل"
        """)
        instructions.setStyleSheet("""
            QLabel {
                color: #1976d2;
                background-color: #e3f2fd;
                padding: 15px;
                border-radius: 8px;
                font-size: 14px;
                border: 2px solid #2196f3;
            }
        """)
        layout.addWidget(instructions)
        
        # إطار كود التفعيل - كبير وواضح جداً
        activation_frame = QFrame()
        activation_frame.setStyleSheet("""
            QFrame {
                background-color: #fff3cd;
                border: 5px solid #ffc107;
                border-radius: 20px;
                padding: 30px;
                margin: 20px 10px;
                min-height: 150px;
            }
        """)
        activation_layout = QVBoxLayout()
        activation_layout.setSpacing(20)

        # عنوان كبير وواضح
        activation_title = QLabel("🔑 أدخل كود التفعيل:")
        activation_title.setFont(QFont("Arial", 20, QFont.Bold))
        activation_title.setAlignment(Qt.AlignCenter)
        activation_title.setStyleSheet("""
            QLabel {
                color: #856404;
                background-color: #ffeaa7;
                padding: 20px;
                border-radius: 12px;
                margin-bottom: 20px;
                border: 3px solid #fdcb6e;
            }
        """)
        activation_layout.addWidget(activation_title)

        # حقل الإدخال - كبير جداً وواضح
        self.activation_code = QLineEdit()
        self.activation_code.setPlaceholderText("SICOO-XXXXXXXX-XXXX-XXXX-XXXX")
        self.activation_code.setMinimumHeight(70)
        self.activation_code.setMaximumHeight(70)
        self.activation_code.setStyleSheet("""
            QLineEdit {
                padding: 25px;
                border: 5px solid #ffc107;
                border-radius: 15px;
                font-size: 20px;
                font-family: 'Courier New', monospace;
                background-color: white;
                color: #2c3e50;
                font-weight: bold;
                text-align: center;
            }
            QLineEdit:focus {
                border-color: #e74c3c;
                background-color: #fff5f5;
                box-shadow: 0 0 15px rgba(231, 76, 60, 0.5);
            }
            QLineEdit:hover {
                border-color: #f39c12;
                background-color: #fffef7;
            }
        """)

        # التأكد من أن الحقل مرئي ومفعل
        self.activation_code.setVisible(True)
        self.activation_code.setEnabled(True)
        self.activation_code.show()
        self.activation_code.setFocus()

        activation_layout.addWidget(self.activation_code)

        # رسالة تأكيد واضحة
        confirm_label = QLabel("👆 اكتب أو الصق كود التفعيل في الحقل أعلاه")
        confirm_label.setAlignment(Qt.AlignCenter)
        confirm_label.setFont(QFont("Arial", 14, QFont.Bold))
        confirm_label.setStyleSheet("""
            QLabel {
                color: #856404;
                background-color: #fef9e7;
                padding: 15px;
                border-radius: 8px;
                margin-top: 15px;
                border: 2px solid #f1c40f;
            }
        """)
        activation_layout.addWidget(confirm_label)

        activation_frame.setLayout(activation_layout)
        layout.addWidget(activation_frame)
        
        # الأزرار
        buttons_layout = QHBoxLayout()
        buttons_layout.setSpacing(20)
        
        # زر نسخ بيانات الجهاز
        copy_data_btn = QPushButton("📋 نسخ بيانات الجهاز")
        copy_data_btn.clicked.connect(self.copy_all_data)
        copy_data_btn.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                padding: 15px 30px;
                border-radius: 10px;
                font-size: 16px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)
        buttons_layout.addWidget(copy_data_btn)
        
        # زر التفعيل
        activate_button = QPushButton("✅ تفعيل البرنامج")
        activate_button.clicked.connect(self.activate_license)
        activate_button.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border: none;
                padding: 15px 30px;
                border-radius: 10px;
                font-size: 16px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)
        buttons_layout.addWidget(activate_button)
        
        # زر إلغاء
        cancel_button = QPushButton("❌ إلغاء")
        cancel_button.clicked.connect(self.reject)
        cancel_button.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                border: none;
                padding: 15px 30px;
                border-radius: 10px;
                font-size: 16px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
        """)
        buttons_layout.addWidget(cancel_button)
        
        layout.addLayout(buttons_layout)
        self.setLayout(layout)
        
        print(f"✅ تم إنشاء حقل كود التفعيل - مرئي: {self.activation_code.isVisible()}, مفعل: {self.activation_code.isEnabled()}")
    
    def copy_to_clipboard(self, text):
        """نسخ النص إلى الحافظة"""
        clipboard = QApplication.clipboard()
        clipboard.setText(text)
        QMessageBox.information(self, "تم النسخ", f"تم نسخ: {text}")
    
    def copy_all_data(self):
        """نسخ جميع بيانات الجهاز"""
        customer_code = self.license_manager.get_customer_code()
        machine_id = self.license_manager.get_machine_id()
        
        data = f"""بيانات الجهاز للتفعيل:
🔑 كود العميل: {customer_code}
💻 رقم الجهاز: {machine_id}

📧 أرسل هذه البيانات إلى: <EMAIL>"""
        
        clipboard = QApplication.clipboard()
        clipboard.setText(data)
        QMessageBox.information(self, "تم النسخ", "تم نسخ جميع بيانات الجهاز إلى الحافظة")
    
    def activate_license(self):
        """تفعيل البرنامج"""
        code = self.activation_code.text().strip()
        
        if not code:
            QMessageBox.warning(self, "تنبيه", "يرجى إدخال كود التفعيل")
            self.activation_code.setFocus()
            return
        
        QMessageBox.information(self, "تم الإدخال", f"تم إدخال الكود: {code}")
        self.accept()

if __name__ == "__main__":
    app = QApplication(sys.argv)
    
    dialog = FixedActivationDialog()
    result = dialog.exec_()
    
    print(f"نتيجة النافذة: {result}")
    sys.exit()
