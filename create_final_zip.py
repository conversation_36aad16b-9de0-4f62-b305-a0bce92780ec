#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إنشاء ملف ZIP للحزمة النهائية
"""

import os
import zipfile
from datetime import datetime

def create_zip_package():
    """إنشاء ملف ZIP للحزمة"""
    
    # مسار الحزمة
    package_path = "dist/نظام_المحاسبة_المتكامل_v1.0_20250715"
    zip_name = f"نظام_المحاسبة_المتكامل_v1.0_{datetime.now().strftime('%Y%m%d')}.zip"
    zip_path = f"dist/{zip_name}"
    
    print("📦 إنشاء ملف ZIP للحزمة النهائية...")
    print(f"📁 المصدر: {package_path}")
    print(f"📄 الملف: {zip_path}")
    print("=" * 50)
    
    try:
        with zipfile.ZipFile(zip_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
            # إضافة جميع الملفات والمجلدات
            for root, dirs, files in os.walk(package_path):
                for file in files:
                    file_path = os.path.join(root, file)
                    # الحصول على المسار النسبي
                    arcname = os.path.relpath(file_path, package_path)
                    zipf.write(file_path, arcname)
                    print(f"✅ تمت إضافة: {arcname}")
        
        # حساب حجم الملف
        file_size = os.path.getsize(zip_path)
        size_mb = file_size / (1024 * 1024)
        
        print("=" * 50)
        print("🎉 تم إنشاء ملف ZIP بنجاح!")
        print(f"📄 اسم الملف: {zip_name}")
        print(f"📏 الحجم: {size_mb:.2f} ميجابايت")
        print(f"📁 المسار: {zip_path}")
        print("=" * 50)
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء ملف ZIP: {e}")
        return False

if __name__ == "__main__":
    create_zip_package()
