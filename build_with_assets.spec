# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['main.py'],
    pathex=[],
    binaries=[],
    datas=[
        # ملفات الإعدادات
        ('theme_settings.json', '.'),
        ('company_settings.json', '.'),
        ('user_settings.json', '.'),
        ('brand_settings.json', '.'),
        ('accounting.db', '.'),
        
        # مجلدات الأصول
        ('assets', 'assets'),
        ('fonts', 'fonts'),
        ('utils', 'utils'),
        ('gui', 'gui'),
        ('database', 'database'),
        
        # ملفات إضافية
        ('images', 'images'),
        ('docs', 'docs'),
        ('sample_data', 'sample_data'),
    ],
    hiddenimports=[
        'PyQt5.QtCore',
        'PyQt5.QtGui', 
        'PyQt5.QtWidgets',
        'PyQt5.QtPrintSupport',
        'PyQt5.QtChart',
        'sqlalchemy',
        'sqlalchemy.dialects.sqlite',
        'pandas',
        'matplotlib',
        'matplotlib.backends.backend_qt5agg',
        'openpyxl',
        'cryptography',
        'utils.theme_manager',
        'utils.settings_manager',
        'gui.main_window',
        'gui.login',
        'gui.dashboard',
        'gui.sales',
        'gui.inventory',
        'gui.purchases',
        'gui.reports',
        'gui.settings_dialog',
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='نظام_المحاسبة_المحدث_v2',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon='assets/icons.ico'
)
