#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار مولد أكواد التفعيل
"""

import sys
import os

# إضافة المسار الحالي
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from license_code_generator import LicenseCodeGenerator
    
    print("🔧 تشغيل مولد أكواد التفعيل...")
    
    app = LicenseCodeGenerator()
    app.run()
    
    print("✅ تم إغلاق مولد الأكواد")
    
except ImportError as e:
    print(f"❌ خطأ في الاستيراد: {e}")
    print("تأكد من وجود ملف license_code_generator.py")
    
except Exception as e:
    print(f"❌ خطأ في تشغيل البرنامج: {e}")

input("اضغط Enter للإغلاق...")
