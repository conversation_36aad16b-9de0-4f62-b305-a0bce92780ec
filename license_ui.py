#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
واجهة المستخدم لنظام التراخيص
License User Interface
"""

import tkinter as tk
from tkinter import messagebox
import webbrowser
import pyperclip
from datetime import datetime
from license_manager import LicenseManager

class LicenseDialog:
    def __init__(self, parent=None):
        self.license_manager = LicenseManager()
        self.parent = parent
        self.result = False
        
    def show_license_expired_dialog(self):
        """عرض نافذة عدم التفعيل"""
        self.window = tk.Toplevel(self.parent) if self.parent else tk.Tk()
        self.window.title("البرنامج غير مفعل")
        self.window.geometry("650x600")  # حجم أكبر لاستيعاب حقل التفعيل
        self.window.resizable(False, False)
        self.window.configure(bg='#f8f9fa')

        # جعل النافذة في المقدمة
        if self.parent:
            self.window.transient(self.parent)
        self.window.grab_set()

        # أيقونة القفل
        icon_frame = tk.Frame(self.window, bg='#f8f9fa')
        icon_frame.pack(pady=20)

        icon_label = tk.Label(
            icon_frame,
            text="🔒",
            font=("Arial", 48),
            bg='#f8f9fa',
            fg='#e74c3c'
        )
        icon_label.pack()

        # العنوان
        title_label = tk.Label(
            self.window,
            text="البرنامج غير مفعل",
            font=("Arial", 18, "bold"),
            bg='#f8f9fa',
            fg='#2c3e50'
        )
        title_label.pack(pady=10)

        # رسالة التوضيح
        message_label = tk.Label(
            self.window,
            text="يرجى تفعيل البرنامج للمتابعة",
            font=("Arial", 12),
            bg='#f8f9fa',
            fg='#7f8c8d'
        )
        message_label.pack(pady=5)
        
        # معلومات الجهاز
        info_frame = tk.Frame(self.window, bg='#f8f9fa')
        info_frame.pack(pady=20, padx=40, fill='x')

        # الحصول على معلومات الجهاز
        customer_code = self.license_manager.get_customer_code()
        machine_id = self.license_manager.get_machine_id()

        # إطار بيانات الجهاز
        data_frame = tk.LabelFrame(
            info_frame,
            text="📋 بيانات الجهاز",
            font=("Arial", 12, "bold"),
            bg='#f8f9fa',
            fg='#2c3e50',
            padx=15,
            pady=10
        )
        data_frame.pack(fill='x', pady=10)

        # كود العميل
        customer_frame = tk.Frame(data_frame, bg='#f8f9fa')
        customer_frame.pack(fill='x', pady=5)

        tk.Label(
            customer_frame,
            text="🔑 كود العميل:",
            font=("Arial", 11, "bold"),
            bg='#f8f9fa',
            fg='#34495e'
        ).pack(side='left')

        customer_code_label = tk.Label(
            customer_frame,
            text=customer_code,
            font=("Courier", 11, "bold"),
            bg='#ecf0f1',
            fg='#2c3e50',
            relief='sunken',
            padx=10,
            pady=2
        )
        customer_code_label.pack(side='right')

        # رقم الجهاز
        machine_frame = tk.Frame(data_frame, bg='#f8f9fa')
        machine_frame.pack(fill='x', pady=5)

        tk.Label(
            machine_frame,
            text="💻 رقم الجهاز:",
            font=("Arial", 11, "bold"),
            bg='#f8f9fa',
            fg='#34495e'
        ).pack(side='left')

        machine_id_label = tk.Label(
            machine_frame,
            text=machine_id,
            font=("Courier", 11, "bold"),
            bg='#ecf0f1',
            fg='#2c3e50',
            relief='sunken',
            padx=10,
            pady=2
        )
        machine_id_label.pack(side='right')

        # رسالة التعليمات
        instructions_frame = tk.Frame(info_frame, bg='#f8f9fa')
        instructions_frame.pack(fill='x', pady=15)

        instructions_text = """📞 للحصول على كود التفعيل:

1️⃣ أرسل البيانات أعلاه إلى المطور
2️⃣ البريد الإلكتروني: <EMAIL>
3️⃣ ستحصل على كود التفعيل خلال 24 ساعة
4️⃣ أدخل الكود في الحقل أدناه واضغط "تفعيل"""

        instructions_label = tk.Label(
            instructions_frame,
            text=instructions_text,
            font=("Arial", 10),
            bg='#f8f9fa',
            fg='#7f8c8d',
            justify='right'
        )
        instructions_label.pack()

        # إطار إدخال كود التفعيل - مباشرة في النافذة الرئيسية
        activation_frame = tk.LabelFrame(
            self.window,
            text="🔐 أدخل كود التفعيل هنا",
            font=("Arial", 14, "bold"),
            bg='#fff3cd',
            fg='#856404',
            padx=20,
            pady=15,
            relief='ridge',
            bd=3
        )
        activation_frame.pack(pady=20, padx=40, fill='x')

        # تعليمات واضحة
        activation_instruction = tk.Label(
            activation_frame,
            text="👇 اكتب أو الصق كود التفعيل في الحقل أدناه:",
            font=("Arial", 12, "bold"),
            bg='#fff3cd',
            fg='#856404'
        )
        activation_instruction.pack(pady=5)

        # حقل إدخال كود التفعيل - كبير وواضح
        self.activation_code_entry = tk.Entry(
            activation_frame,
            font=("Courier", 14, "bold"),
            width=35,
            justify='center',
            bg='white',
            fg='#2c3e50',
            relief='solid',
            bd=2,
            highlightthickness=2,
            highlightcolor='#e74c3c'
        )
        self.activation_code_entry.pack(pady=10, ipady=8)
        self.activation_code_entry.insert(0, "SICOO-XXXXXXXX-XXXX-XXXX-XXXX")
        self.activation_code_entry.bind('<FocusIn>', lambda e: self.activation_code_entry.delete(0, 'end') if self.activation_code_entry.get() == "SICOO-XXXXXXXX-XXXX-XXXX-XXXX" else None)

        # رسالة تأكيد
        confirm_label = tk.Label(
            activation_frame,
            text="👆 أدخل كود التفعيل في الحقل أعلاه",
            font=("Arial", 10),
            bg='#fff3cd',
            fg='#856404'
        )
        confirm_label.pack(pady=5)

        # الأزرار
        buttons_frame = tk.Frame(self.window, bg='#f8f9fa')
        buttons_frame.pack(pady=25)

        # زر تفعيل البرنامج - يستخدم الحقل المباشر
        activate_btn = tk.Button(
            buttons_frame,
            text="✅ تفعيل البرنامج",
            font=("Arial", 14, "bold"),
            bg='#27ae60',
            fg='white',
            padx=30,
            pady=12,
            command=self.activate_license_direct
        )
        activate_btn.pack(side='left', padx=10)

        # زر نسخ بيانات الجهاز
        copy_btn = tk.Button(
            buttons_frame,
            text="📋 نسخ بيانات الجهاز",
            font=("Arial", 12),
            bg='#3498db',
            fg='white',
            padx=20,
            pady=10,
            command=lambda: self.copy_machine_data(customer_code, machine_id)
        )
        copy_btn.pack(side='left', padx=10)

        # زر إغلاق
        close_btn = tk.Button(
            buttons_frame,
            text="❌ إغلاق",
            font=("Arial", 12),
            bg='#e74c3c',
            fg='white',
            padx=20,
            pady=10,
            command=self.close_application
        )
        close_btn.pack(side='left', padx=10)
        
        # جعل النافذة modal (تمنع التفاعل مع النوافذ الأخرى)
        if self.parent:
            self.window.grab_set()

        # تشغيل النافذة
        self.window.mainloop()

    def activate_license_direct(self):
        """تفعيل البرنامج مباشرة من الحقل في النافذة الرئيسية"""
        if not hasattr(self, 'activation_code_entry'):
            messagebox.showerror("خطأ", "حقل كود التفعيل غير موجود")
            return

        license_code = self.activation_code_entry.get().strip()

        # التحقق من أن الكود ليس النص التوضيحي
        if not license_code or license_code == "SICOO-XXXXXXXX-XXXX-XXXX-XXXX":
            messagebox.showerror("خطأ", "يرجى إدخال كود التفعيل الصحيح")
            self.activation_code_entry.focus()
            return

        # الحصول على بيانات الجهاز
        customer_code = self.license_manager.get_customer_code()
        machine_id = self.license_manager.get_machine_id()

        # التحقق من صحة الكود
        result = self.validate_license_code(license_code, customer_code, machine_id)

        if result["valid"]:
            # حفظ الترخيص
            if self.save_license(result["license_data"]):
                messagebox.showinfo(
                    "تم التفعيل بنجاح! 🎉",
                    f"تم تفعيل البرنامج بنجاح!\n\n"
                    f"📅 صالح حتى: {result['expiry_date'].strftime('%d/%m/%Y')}\n"
                    f"⏰ متبقي: {result['days_remaining']} يوم"
                )
                self.result = True
                if self.window:
                    self.window.quit()
                    self.window.destroy()
            else:
                messagebox.showerror("خطأ", "فشل في حفظ الترخيص")
        else:
            messagebox.showerror("كود خاطئ", result["message"])
            self.activation_code_entry.focus()

    def show_activation_dialog(self):
        """عرض نافذة إدخال كود التفعيل"""
        activation_window = tk.Toplevel(self.window) if self.window else tk.Tk()
        activation_window.title("تفعيل البرنامج")
        activation_window.geometry("500x400")
        activation_window.resizable(False, False)
        activation_window.configure(bg='#f8f9fa')

        # جعل النافذة في المقدمة
        if self.window:
            activation_window.transient(self.window)
            activation_window.grab_set()

        # العنوان
        title_label = tk.Label(
            activation_window,
            text="🔑 تفعيل البرنامج",
            font=("Arial", 18, "bold"),
            bg='#f8f9fa',
            fg='#2c3e50'
        )
        title_label.pack(pady=20)

        # معلومات الجهاز (للمراجعة)
        info_frame = tk.LabelFrame(
            activation_window,
            text="📋 بيانات الجهاز",
            font=("Arial", 12, "bold"),
            bg='#f8f9fa',
            fg='#2c3e50',
            padx=15,
            pady=10
        )
        info_frame.pack(pady=10, padx=30, fill='x')

        customer_code = self.license_manager.get_customer_code()
        machine_id = self.license_manager.get_machine_id()

        # كود العميل
        customer_frame = tk.Frame(info_frame, bg='#f8f9fa')
        customer_frame.pack(fill='x', pady=3)

        tk.Label(
            customer_frame,
            text="🔑 كود العميل:",
            font=("Arial", 11, "bold"),
            bg='#f8f9fa',
            fg='#34495e'
        ).pack(side='left')

        tk.Label(
            customer_frame,
            text=customer_code,
            font=("Courier", 11),
            bg='#f8f9fa',
            fg='#2c3e50'
        ).pack(side='right')

        # رقم الجهاز
        machine_frame = tk.Frame(info_frame, bg='#f8f9fa')
        machine_frame.pack(fill='x', pady=3)

        tk.Label(
            machine_frame,
            text="💻 رقم الجهاز:",
            font=("Arial", 11, "bold"),
            bg='#f8f9fa',
            fg='#34495e'
        ).pack(side='left')

        tk.Label(
            machine_frame,
            text=machine_id,
            font=("Courier", 11),
            bg='#f8f9fa',
            fg='#2c3e50'
        ).pack(side='right')

        # إطار إدخال كود التفعيل
        code_frame = tk.LabelFrame(
            activation_window,
            text="🔐 كود التفعيل",
            font=("Arial", 12, "bold"),
            bg='#f8f9fa',
            fg='#2c3e50',
            padx=15,
            pady=10
        )
        code_frame.pack(pady=15, padx=30, fill='x')

        tk.Label(
            code_frame,
            text="أدخل كود التفعيل الذي حصلت عليه من المطور:",
            font=("Arial", 10),
            bg='#f8f9fa',
            fg='#7f8c8d'
        ).pack(pady=5)

        # خانة إدخال الكود
        code_entry = tk.Entry(
            code_frame,
            font=("Courier", 12),
            width=40,
            justify='center'
        )
        code_entry.pack(pady=10)
        code_entry.focus()

        # دالة التفعيل
        def activate_license():
            license_code = code_entry.get().strip()
            if not license_code:
                messagebox.showerror("خطأ", "يرجى إدخال كود التفعيل")
                return

            # التحقق من صحة الكود
            result = self.validate_license_code(license_code, customer_code, machine_id)

            if result["valid"]:
                # حفظ الترخيص
                if self.save_license(result["license_data"]):
                    messagebox.showinfo(
                        "تم التفعيل بنجاح! 🎉",
                        f"تم تفعيل البرنامج بنجاح!\n\n"
                        f"📅 صالح حتى: {result['expiry_date'].strftime('%d/%m/%Y')}\n"
                        f"⏰ متبقي: {result['days_remaining']} يوم"
                    )
                    self.result = True
                    activation_window.destroy()
                    if self.window:
                        self.window.quit()
                        self.window.destroy()
                else:
                    messagebox.showerror("خطأ", "فشل في حفظ الترخيص")
            else:
                messagebox.showerror("كود خاطئ", result["message"])

        # الأزرار
        buttons_frame = tk.Frame(activation_window, bg='#f8f9fa')
        buttons_frame.pack(pady=20)

        # زر التفعيل
        activate_btn = tk.Button(
            buttons_frame,
            text="✅ تفعيل",
            font=("Arial", 12, "bold"),
            bg='#27ae60',
            fg='white',
            padx=30,
            pady=8,
            command=activate_license
        )
        activate_btn.pack(side='left', padx=10)

        # زر إلغاء
        cancel_btn = tk.Button(
            buttons_frame,
            text="❌ إلغاء",
            font=("Arial", 12),
            bg='#e74c3c',
            fg='white',
            padx=30,
            pady=8,
            command=activation_window.destroy
        )
        cancel_btn.pack(side='left', padx=10)

    def show_renewal_dialog(self):
        """عرض نافذة إدخال كود التجديد"""
        if not hasattr(self, 'window'):
            self.window = None
        renewal_window = tk.Toplevel(self.window) if self.window else tk.Tk()
        renewal_window.title("تجديد ترخيص البرنامج")
        renewal_window.geometry("450x350")
        renewal_window.resizable(False, False)
        renewal_window.configure(bg='#f8f9fa')
        
        # جعل النافذة في المقدمة
        if self.window:
            renewal_window.transient(self.window)
            renewal_window.grab_set()
        
        # العنوان
        title_label = tk.Label(
            renewal_window,
            text="🔑 تجديد ترخيص البرنامج",
            font=("Arial", 16, "bold"),
            bg='#f8f9fa',
            fg='#2c3e50'
        )
        title_label.pack(pady=20)
        
        # معلومات العميل
        info_frame = tk.Frame(renewal_window, bg='#f8f9fa')
        info_frame.pack(pady=10, padx=30, fill='x')
        
        customer_code = self.license_manager.get_customer_code()
        machine_id = self.license_manager.get_machine_id()
        
        # كود العميل
        tk.Label(
            info_frame,
            text="📝 كود العميل:",
            font=("Arial", 11, "bold"),
            bg='#f8f9fa',
            anchor='e'
        ).grid(row=0, column=0, sticky='e', padx=10, pady=5)
        
        customer_entry = tk.Entry(
            info_frame,
            font=("Arial", 11),
            width=20,
            state='readonly'
        )
        customer_entry.insert(0, customer_code)
        customer_entry.grid(row=0, column=1, padx=10, pady=5)
        
        # رقم الجهاز
        tk.Label(
            info_frame,
            text="💻 رقم الجهاز:",
            font=("Arial", 11, "bold"),
            bg='#f8f9fa',
            anchor='e'
        ).grid(row=1, column=0, sticky='e', padx=10, pady=5)
        
        machine_entry = tk.Entry(
            info_frame,
            font=("Arial", 11),
            width=20,
            state='readonly'
        )
        machine_entry.insert(0, machine_id)
        machine_entry.grid(row=1, column=1, padx=10, pady=5)
        
        # كود التجديد
        tk.Label(
            info_frame,
            text="🔐 كود التجديد:",
            font=("Arial", 11, "bold"),
            bg='#f8f9fa',
            anchor='e'
        ).grid(row=2, column=0, sticky='e', padx=10, pady=15)
        
        renewal_code_entry = tk.Entry(
            info_frame,
            font=("Arial", 11),
            width=30
        )
        renewal_code_entry.grid(row=2, column=1, padx=10, pady=15)
        renewal_code_entry.focus()
        
        # معلومات التجديد
        current_status = self.license_manager.check_license()
        current_expiry = current_status.get("expiry_date")
        if current_expiry:
            current_expiry_str = current_expiry.strftime("%d/%m/%Y")
        else:
            current_expiry_str = "غير محدد"
        
        status_frame = tk.Frame(renewal_window, bg='#f8f9fa')
        status_frame.pack(pady=10)
        
        status_text = f"""
📅 الترخيص الحالي ينتهي في: {current_expiry_str}
📅 بعد التجديد سينتهي في: 31/12/{datetime.now().year + 1}
        """
        
        tk.Label(
            status_frame,
            text=status_text,
            font=("Arial", 10),
            bg='#f8f9fa',
            fg='#7f8c8d',
            justify='center'
        ).pack()
        
        # الأزرار
        buttons_frame = tk.Frame(renewal_window, bg='#f8f9fa')
        buttons_frame.pack(pady=20)
        
        def activate_license():
            renewal_code = renewal_code_entry.get().strip()
            if not renewal_code:
                messagebox.showerror("خطأ", "يرجى إدخال كود التجديد")
                return
            
            result = self.license_manager.apply_renewal_code(renewal_code)
            
            if result["valid"]:
                messagebox.showinfo(
                    "نجح التفعيل",
                    f"تم تجديد الترخيص بنجاح!\n"
                    f"صالح حتى: {result['new_expiry'].strftime('%d/%m/%Y')}"
                )
                self.result = True
                renewal_window.destroy()
                if self.window:
                    self.window.quit()  # إنهاء mainloop
                    self.window.destroy()
            else:
                messagebox.showerror("فشل التفعيل", result["message"])
        
        # زر التفعيل
        activate_btn = tk.Button(
            buttons_frame,
            text="تفعيل",
            font=("Arial", 12, "bold"),
            bg='#27ae60',
            fg='white',
            padx=30,
            pady=8,
            command=activate_license
        )
        activate_btn.pack(side='left', padx=10)
        
        # زر إلغاء
        cancel_btn = tk.Button(
            buttons_frame,
            text="إلغاء",
            font=("Arial", 12),
            bg='#95a5a6',
            fg='white',
            padx=30,
            pady=8,
            command=renewal_window.destroy
        )
        cancel_btn.pack(side='left', padx=10)
        
        # زر تواصل معنا
        contact_btn = tk.Button(
            buttons_frame,
            text="تواصل معنا",
            font=("Arial", 12),
            bg='#3498db',
            fg='white',
            padx=20,
            pady=8,
            command=self.open_contact
        )
        contact_btn.pack(side='left', padx=10)
        
        # ربط مفتاح Enter بالتفعيل
        renewal_code_entry.bind('<Return>', lambda e: activate_license())
    
    def copy_machine_data(self, customer_code, machine_id):
        """نسخ بيانات الجهاز إلى الحافظة"""
        data = f"كود العميل: {customer_code}\nرقم الجهاز: {machine_id}"
        try:
            pyperclip.copy(data)
            messagebox.showinfo(
                "تم النسخ",
                "تم نسخ بيانات الجهاز إلى الحافظة\n\n"
                "أرسل هذه البيانات إلى:\n"
                "📧 <EMAIL>\n\n"
                "للحصول على كود التفعيل"
            )
        except Exception:
            # إذا فشل pyperclip، عرض البيانات في نافذة
            messagebox.showinfo("بيانات الجهاز", data)
    
    def open_contact(self):
        """فتح وسائل التواصل"""
        try:
            # فتح الإيميل مباشرة
            webbrowser.open("mailto:<EMAIL>?subject=طلب تجديد ترخيص برنامج المحاسبة")
        except Exception:
            messagebox.showinfo(
                "معلومات التواصل",
                "📧 البريد الإلكتروني: <EMAIL>\n\n"
                "أرسل إيميل يحتوي على:\n"
                "• كود العميل ورقم الجهاز\n"
                "• إثبات الدفع\n"
                "• رقم هاتفك للتواصل"
            )
    
    def close_application(self):
        """إغلاق التطبيق بدون تفعيل"""
        # تعيين النتيجة كـ False (لم يتم التفعيل)
        self.result = False

        # إغلاق النوافذ
        if hasattr(self, 'window') and self.window:
            self.window.quit()  # استخدام quit بدلاً من destroy لإنهاء mainloop
            self.window.destroy()
        if self.parent:
            self.parent.destroy()

    def validate_license_code(self, license_code, customer_code, machine_id):
        """التحقق من صحة كود الترخيص"""
        try:
            # تنسيق الكود المتوقع: SICOO-YYYYMMDD-CUSTOMER-MACHINE-HASH
            parts = license_code.strip().upper().split('-')

            if len(parts) != 5 or parts[0] != "SICOO":
                return {
                    "valid": False,
                    "message": "تنسيق كود الترخيص غير صحيح"
                }

            date_str = parts[1]
            customer_part = parts[2]
            machine_part = parts[3]
            hash_part = parts[4]

            # التحقق من التاريخ
            try:
                from datetime import datetime
                expiry_date = datetime.strptime(date_str, "%Y%m%d")
                current_date = datetime.now()

                if current_date > expiry_date:
                    return {
                        "valid": False,
                        "message": "كود الترخيص منتهي الصلاحية"
                    }

                days_remaining = (expiry_date - current_date).days

            except ValueError:
                return {
                    "valid": False,
                    "message": "تاريخ انتهاء الترخيص غير صحيح"
                }

            # التحقق من كود العميل ورقم الجهاز
            expected_customer = customer_code[:6].upper()
            expected_machine = machine_id[:6].upper()

            if customer_part != expected_customer:
                return {
                    "valid": False,
                    "message": "كود الترخيص غير صالح لهذا العميل"
                }

            if machine_part != expected_machine:
                return {
                    "valid": False,
                    "message": "كود الترخيص غير صالح لهذا الجهاز"
                }

            # التحقق من الـ hash
            import hashlib
            data_to_hash = f"{customer_code}{machine_id}{date_str}SICOO_LICENSE_2024"
            expected_hash = hashlib.sha256(data_to_hash.encode()).hexdigest()[:8].upper()

            if hash_part != expected_hash:
                return {
                    "valid": False,
                    "message": "كود الترخيص غير صحيح أو تالف"
                }

            # إنشاء بيانات الترخيص
            license_data = {
                "customer_code": customer_code,
                "machine_id": machine_id,
                "expiry_date": expiry_date.isoformat(),
                "license_type": "FULL",
                "license_code": license_code,
                "activated_date": datetime.now().isoformat()
            }

            return {
                "valid": True,
                "message": "كود الترخيص صحيح",
                "expiry_date": expiry_date,
                "days_remaining": days_remaining,
                "license_data": license_data
            }

        except Exception as e:
            return {
                "valid": False,
                "message": f"خطأ في التحقق من الكود: {str(e)}"
            }

    def save_license(self, license_data):
        """حفظ بيانات الترخيص"""
        try:
            return self.license_manager._save_license(license_data)
        except Exception as e:
            print(f"خطأ في حفظ الترخيص: {e}")
            return False

def check_license_and_show_dialog():
    """
    يفحص حالة الترخيص. إذا كان صالحًا، يُرجع True.
    إذا كان غير صالح، يعرض نافذة التفعيل ويُرجع False إذا لم يتم التفعيل.
    """
    try:
        from license_manager import LicenseManager

        # إنشاء مدير التراخيص
        license_manager = LicenseManager()

        # فحص الترخيص
        status = license_manager.check_license()

        if status["valid"]:
            # الترخيص صالح
            return True
        else:
            # الترخيص غير صالح - عرض نافذة التفعيل
            print(f"❌ الترخيص غير صالح: {status['message']}")

            # إنشاء نافذة التفعيل
            import tkinter as tk
            from tkinter import messagebox

            # إخفاء النافذة الرئيسية لـ tkinter
            root = tk.Tk()
            root.withdraw()

            try:
                # عرض نافذة التفعيل
                license_dialog = LicenseDialog()

                # عرض نافذة انتهاء الصلاحية
                license_dialog.show_license_expired_dialog()

                # إذا تم التفعيل بنجاح، إرجاع True
                if license_dialog.result:
                    root.destroy()
                    return True
                else:
                    # لم يتم التفعيل - إرجاع False
                    root.destroy()
                    return False

            except Exception as e:
                print(f"خطأ في عرض نافذة التفعيل: {e}")
                root.destroy()
                return False

    except ImportError:
        # نظام التراخيص غير متوفر
        print("⚠️ نظام التراخيص غير متوفر")
        return True
    except Exception as e:
        print(f"خطأ في فحص الترخيص: {e}")
        return False

# مثال على الاستخدام
if __name__ == '__main__':
    # فحص الترخيص عند بداية البرنامج
    if check_license_and_show_dialog():
        print("البرنامج مرخص ويمكن تشغيله")
    else:
        print("لم يتم تفعيل الترخيص - إغلاق البرنامج")
