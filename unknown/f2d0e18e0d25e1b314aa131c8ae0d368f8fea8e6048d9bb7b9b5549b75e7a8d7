# 🔐 نظام التراخيص المتكامل

## 📋 نظرة عامة

نظام تراخيص متكامل لحماية نظام المحاسبة يتكون من برنامجين منفصلين:

1. **برنامج التفعيل للعملاء** - لتفعيل البرنامج
2. **مولد الأكواد للمطور** - لتوليد أكواد التفعيل

## 📁 الملفات المُصدَّرة

### للعملاء:
- `برنامج_تفعيل_التراخيص.exe` - برنامج التفعيل
- `تعليمات_برنامج_التفعيل.txt` - تعليمات الاستخدام

### للمطور:
- `مولد_أكواد_التفعيل_للمطور.exe` - مولد الأكواد
- `تعليمات_مولد_الأكواد_للمطور.txt` - تعليمات الاستخدام

## 🔧 للمطور

### استخدام مولد الأكواد:

1. **تشغيل البرنامج:**
   ```
   مولد_أكواد_التفعيل_للمطور.exe
   ```

2. **إدخال بيانات العميل:**
   - كود العميل (من برنامج التفعيل)
   - رقم الجهاز (من برنامج التفعيل)
   - مدة الترخيص بالأيام
   - اسم العميل (اختياري)

3. **توليد الكود:**
   - اضغط "توليد كود التفعيل"
   - انسخ الكود المُولد
   - أرسله للعميل

### مميزات مولد الأكواد:

- ✅ توليد أكواد آمنة مشفرة
- ✅ سجل تلقائي للأكواد المُولدة
- ✅ واجهة سهلة الاستخدام
- ✅ تتبع العملاء والتواريخ

## 👥 للعملاء

### استخدام برنامج التفعيل:

1. **تشغيل البرنامج:**
   ```
   برنامج_تفعيل_التراخيص.exe
   ```

2. **نسخ بيانات الجهاز:**
   - انسخ "كود العميل" و "رقم الجهاز"
   - أرسلهما للمطور

3. **إدخال كود التفعيل:**
   - أدخل الكود المُستلم من المطور
   - اضغط "تفعيل البرنامج"

4. **نسخ ملف الترخيص:**
   - انسخ ملف `license.dat` إلى مجلد نظام المحاسبة

## 🔐 آلية عمل النظام

### تنسيق كود التفعيل:
```
SICOO-YYYYMMDD-XXXX-XXXX-XXXXXXXX
```

حيث:
- `SICOO`: بادئة ثابتة
- `YYYYMMDD`: تاريخ انتهاء الصلاحية
- `XXXX`: جزء من كود العميل
- `XXXX`: جزء من رقم الجهاز  
- `XXXXXXXX`: هاش التحقق SHA256

### الأمان:
- ✅ ربط بمعرف الجهاز الفريد
- ✅ تشفير قوي للبيانات
- ✅ منع إعادة استخدام الأكواد
- ✅ تاريخ انتهاء صلاحية

## 📧 سير العمل

1. **العميل يطلب التفعيل:**
   - يشغل برنامج التفعيل
   - ينسخ بيانات الجهاز
   - يرسلها للمطور

2. **المطور يولد الكود:**
   - يستخدم مولد الأكواد
   - يدخل بيانات العميل
   - يولد كود التفعيل
   - يرسله للعميل

3. **العميل يفعل البرنامج:**
   - يدخل الكود في برنامج التفعيل
   - ينسخ ملف الترخيص
   - يشغل نظام المحاسبة

## 📊 إحصائيات النظام

- 🔒 **الأمان:** تشفير SHA256 + ربط بالجهاز
- ⏰ **المرونة:** مدد ترخيص متنوعة (30-1095 يوم)
- 📋 **التتبع:** سجل كامل للأكواد المُولدة
- 🎯 **السهولة:** واجهات بسيطة وواضحة

## ⚠️ ملاحظات مهمة

### للمطور:
- احتفظ بمولد الأكواد آمناً
- لا تشارك البرنامج مع أحد
- احتفظ بنسخة احتياطية من السجل

### للعملاء:
- كل كود يعمل على جهاز واحد فقط
- لا يمكن استخدام الكود أكثر من مرة
- احتفظ بنسخة احتياطية من ملف الترخيص

## 📞 الدعم الفني

**البريد الإلكتروني:** <EMAIL>

**ساعات العمل:** 24/7

**وقت الاستجابة:** خلال 24 ساعة

---

✅ **تم تطوير النظام بواسطة:**
SICOO Company - نظام المحاسبة المتقدم
تاريخ الإنشاء: 2025/01/15
