#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إنشاء حزمة التوزيع المحدثة مع النسخة الجديدة
"""

import os
import shutil
import json
from datetime import datetime

class UpdatedPackageCreator:
    def __init__(self):
        self.package_name = "نظام_المحاسبة_المحدث"
        self.version = "2.0"
        self.build_date = datetime.now().strftime("%Y%m%d")
        
        # مسار الحزمة النهائية
        self.package_path = f"dist/{self.package_name}_v{self.version}_{self.build_date}"
        
    def create_package_structure(self):
        """إنشاء هيكل الحزمة"""
        print("📁 إنشاء هيكل الحزمة المحدثة...")
        
        # إنشاء المجلدات الرئيسية
        folders = [
            self.package_path,
            f"{self.package_path}/النظام",
            f"{self.package_path}/الأصول",
            f"{self.package_path}/الأصول/الشعارات",
            f"{self.package_path}/الأصول/الخطوط",
            f"{self.package_path}/الأصول/الأيقونات",
            f"{self.package_path}/التوثيق",
            f"{self.package_path}/الإعدادات",
            f"{self.package_path}/النسخ_الاحتياطية"
        ]
        
        for folder in folders:
            os.makedirs(folder, exist_ok=True)
            print(f"✅ تم إنشاء: {folder}")
    
    def copy_updated_system(self):
        """نسخ النظام المحدث الجديد"""
        print("🔧 نسخ النظام المحدث...")
        
        # نسخ النسخة الجديدة المحدثة مع جميع الأصول
        source_exe = "dist/نظام_المحاسبة_المحدث_v2.exe"
        target_exe = f"{self.package_path}/النظام/نظام المحاسبة.exe"
        
        if os.path.exists(source_exe):
            shutil.copy2(source_exe, target_exe)
            print("✅ تم نسخ النسخة المحدثة من النظام")
        else:
            print("❌ النسخة المحدثة غير موجودة")
            return False
        
        # نسخ قاعدة البيانات الحالية
        if os.path.exists('accounting.db'):
            target_db = f"{self.package_path}/النظام/accounting.db"
            shutil.copy2('accounting.db', target_db)
            print("✅ تم نسخ قاعدة البيانات")
        
        return True
    
    def copy_activation_program(self):
        """نسخ برنامج التفعيل"""
        print("🔑 نسخ برنامج التفعيل...")
        
        source_activation = "dist/برنامج_تفعيل_التراخيص.exe"
        target_activation = f"{self.package_path}/برنامج_تفعيل_التراخيص.exe"
        
        if os.path.exists(source_activation):
            shutil.copy2(source_activation, target_activation)
            print("✅ تم نسخ برنامج التفعيل")
        else:
            print("❌ برنامج التفعيل غير موجود")
    
    def copy_assets(self):
        """نسخ الأصول"""
        print("🎨 نسخ الأصول...")
        
        # نسخ الشعارات
        if os.path.exists('assets'):
            target_assets = f"{self.package_path}/الأصول/الشعارات"
            for file in os.listdir('assets'):
                if file.endswith(('.png', '.jpg', '.jpeg', '.ico')):
                    shutil.copy2(f"assets/{file}", f"{target_assets}/{file}")
            print("✅ تم نسخ الشعارات")
        
        # نسخ الخطوط
        if os.path.exists('fonts'):
            target_fonts = f"{self.package_path}/الأصول/الخطوط"
            shutil.copytree('fonts', target_fonts, dirs_exist_ok=True)
            print("✅ تم نسخ الخطوط")
    
    def create_default_settings(self):
        """إنشاء الإعدادات الافتراضية"""
        print("⚙️ إنشاء الإعدادات الافتراضية...")
        
        # إعدادات الشركة الافتراضية
        company_settings = {
            "company_name": "اسم الشركة",
            "company_address": "عنوان الشركة",
            "company_phone": "رقم الهاتف",
            "company_email": "البريد الإلكتروني",
            "tax_number": "الرقم الضريبي",
            "currency": "جنيه مصري",
            "currency_symbol": "ج.م"
        }
        
        # إعدادات النظام الافتراضية
        system_settings = {
            "theme": "modern",
            "language": "ar",
            "auto_backup": True,
            "backup_interval": 7,
            "print_format": "A4",
            "date_format": "dd/mm/yyyy"
        }
        
        # حفظ الإعدادات
        settings_path = f"{self.package_path}/الإعدادات"
        
        with open(f"{settings_path}/company_settings_default.json", 'w', encoding='utf-8') as f:
            json.dump(company_settings, f, ensure_ascii=False, indent=2)
        
        with open(f"{settings_path}/system_settings_default.json", 'w', encoding='utf-8') as f:
            json.dump(system_settings, f, ensure_ascii=False, indent=2)
        
        print("✅ تم إنشاء الإعدادات الافتراضية")
    
    def create_documentation(self):
        """إنشاء التوثيق"""
        print("📚 إنشاء التوثيق...")
        
        # دليل التثبيت
        installation_guide = """
🔧 دليل تثبيت نظام المحاسبة المحدث v2.0
==========================================

📋 متطلبات النظام:
- Windows 7 أو أحدث
- مساحة قرص: 500 ميجابايت
- ذاكرة: 2 جيجابايت RAM

🚀 خطوات التثبيت:

1️⃣ تفعيل البرنامج:
   - شغل "برنامج_تفعيل_التراخيص.exe"
   - انسخ بيانات الجهاز وأرسلها للمطور
   - أدخل كود التفعيل المُستلم
   - انسخ ملف "license.dat" لمجلد النظام

2️⃣ تشغيل النظام:
   - ادخل لمجلد "النظام"
   - شغل "نظام المحاسبة.exe"
   - أدخل بيانات الشركة في الإعداد الأولي

3️⃣ الإعداد الأولي:
   - أدخل بيانات الشركة
   - اختر الإعدادات المناسبة
   - ابدأ استخدام النظام

🆕 الجديد في الإصدار 2.0:
- تحسينات في واجهة المستخدم
- إصلاح مشاكل التفعيل
- تحسين أداء النظام
- إضافة مميزات جديدة

📞 للدعم الفني:
البريد الإلكتروني: <EMAIL>
        """
        
        # دليل المستخدم المختصر
        user_guide = """
📖 دليل المستخدم السريع - الإصدار المحدث
========================================

🏠 الصفحة الرئيسية:
- عرض الإحصائيات اليومية
- الوصول السريع للوظائف

💰 المبيعات:
- إنشاء فواتير البيع
- إدارة العملاء
- تتبع المدفوعات

📦 المخزون:
- إدارة المنتجات
- تتبع الكميات
- تقارير المخزون

📊 التقارير:
- تقارير المبيعات
- تقارير المخزون
- التقارير المالية

⚙️ الإعدادات:
- بيانات الشركة
- إعدادات النظام
- النسخ الاحتياطية

🆕 المميزات الجديدة:
- واجهة محسنة
- أداء أفضل
- استقرار أكثر
        """
        
        # حفظ التوثيق
        docs_path = f"{self.package_path}/التوثيق"
        
        with open(f"{docs_path}/دليل_التثبيت.txt", 'w', encoding='utf-8') as f:
            f.write(installation_guide)
        
        with open(f"{docs_path}/دليل_المستخدم_السريع.txt", 'w', encoding='utf-8') as f:
            f.write(user_guide)
        
        print("✅ تم إنشاء التوثيق")
    
    def create_batch_files(self):
        """إنشاء ملفات التشغيل"""
        print("⚡ إنشاء ملفات التشغيل...")
        
        # ملف تشغيل النظام
        run_system_bat = """@echo off
echo 🚀 تشغيل نظام المحاسبة المحدث v2.0...
echo.

cd /d "%~dp0النظام"

if not exist "نظام المحاسبة.exe" (
    echo ❌ ملف النظام غير موجود
    pause
    exit /b 1
)

if not exist "license.dat" (
    echo ⚠️ لم يتم تفعيل البرنامج بعد
    echo يرجى تشغيل برنامج التفعيل أولاً
    pause
    exit /b 1
)

echo ✅ تشغيل النظام المحدث...
start "" "نظام المحاسبة.exe"
"""
        
        # ملف التفعيل
        activation_bat = """@echo off
echo 🔑 تفعيل نظام المحاسبة المحدث...
echo.

if not exist "برنامج_تفعيل_التراخيص.exe" (
    echo ❌ برنامج التفعيل غير موجود
    pause
    exit /b 1
)

echo ✅ تشغيل برنامج التفعيل...
start "" "برنامج_تفعيل_التراخيص.exe"
"""
        
        # حفظ ملفات التشغيل
        with open(f"{self.package_path}/تشغيل_النظام.bat", 'w', encoding='utf-8') as f:
            f.write(run_system_bat)
        
        with open(f"{self.package_path}/تفعيل_البرنامج.bat", 'w', encoding='utf-8') as f:
            f.write(activation_bat)
        
        print("✅ تم إنشاء ملفات التشغيل")
    
    def create_package_info(self):
        """إنشاء معلومات الحزمة"""
        print("📋 إنشاء معلومات الحزمة...")
        
        package_info = {
            "name": "نظام المحاسبة المحدث",
            "version": self.version,
            "build_date": self.build_date,
            "developer": "SICOO Company",
            "email": "<EMAIL>",
            "description": "نظام محاسبة متكامل ومحدث للشركات الصغيرة والمتوسطة",
            "changelog": [
                "تحسينات في واجهة المستخدم",
                "إصلاح مشاكل التفعيل",
                "تحسين أداء النظام",
                "إضافة مميزات جديدة",
                "استقرار أكثر"
            ],
            "features": [
                "إدارة المبيعات والمشتريات",
                "إدارة المخزون",
                "التقارير المالية",
                "نظام تراخيص آمن",
                "واجهة عربية سهلة"
            ],
            "requirements": {
                "os": "Windows 7+",
                "ram": "2GB",
                "disk": "500MB",
                "screen": "1024x768"
            }
        }
        
        with open(f"{self.package_path}/معلومات_الحزمة.json", 'w', encoding='utf-8') as f:
            json.dump(package_info, f, ensure_ascii=False, indent=2)
        
        print("✅ تم إنشاء معلومات الحزمة")
    
    def create_readme(self):
        """إنشاء ملف README"""
        readme_content = """🎉 مرحباً بك في نظام المحاسبة المحدث v2.0
==========================================

📋 نظرة عامة:
نظام محاسبة شامل ومتطور للشركات الصغيرة والمتوسطة
يحتوي على جميع الأدوات اللازمة لإدارة أعمالك بكفاءة

🆕 الجديد في هذا الإصدار:
- تحسينات كبيرة في واجهة المستخدم
- إصلاح جميع مشاكل التفعيل
- تحسين أداء النظام بشكل ملحوظ
- إضافة مميزات جديدة
- استقرار أكثر وأخطاء أقل

🚀 البدء السريع:

1️⃣ تفعيل البرنامج (مطلوب):
   - انقر نقراً مزدوجاً على "تفعيل_البرنامج.bat"
   - أو شغل "برنامج_تفعيل_التراخيص.exe" مباشرة
   - انسخ بيانات الجهاز وأرسلها للمطور
   - أدخل كود التفعيل المُستلم

2️⃣ تشغيل النظام:
   - انقر نقراً مزدوجاً على "تشغيل_النظام.bat"
   - أو ادخل مجلد "النظام" وشغل "نظام المحاسبة.exe"

3️⃣ الإعداد الأولي:
   - أدخل بيانات شركتك
   - اختر الإعدادات المناسبة
   - ابدأ استخدام النظام

📞 الدعم الفني:
📧 البريد الإلكتروني: <EMAIL>
⏰ ساعات العمل: 24/7
🕒 وقت الاستجابة: خلال 24 ساعة

🎉 شكراً لاختيارك نظام المحاسبة المحدث!

---
✅ تم تطوير النظام بواسطة: SICOO Company
📅 تاريخ الإصدار: 15/01/2025
🔢 رقم الإصدار: 2.0"""
        
        with open(f"{self.package_path}/اقرأني_أولاً.txt", 'w', encoding='utf-8') as f:
            f.write(readme_content)
        
        print("✅ تم إنشاء ملف README")
    
    def create_updated_package(self):
        """إنشاء الحزمة المحدثة"""
        print("🎯 بدء إنشاء الحزمة المحدثة...")
        print("=" * 50)
        
        try:
            self.create_package_structure()
            
            if not self.copy_updated_system():
                return False
                
            self.copy_activation_program()
            self.copy_assets()
            self.create_default_settings()
            self.create_documentation()
            self.create_batch_files()
            self.create_package_info()
            self.create_readme()
            
            print("=" * 50)
            print("🎉 تم إنشاء الحزمة المحدثة بنجاح!")
            print(f"📁 المسار: {self.package_path}")
            print("=" * 50)
            
            return True
            
        except Exception as e:
            print(f"❌ خطأ في إنشاء الحزمة: {e}")
            return False

if __name__ == "__main__":
    creator = UpdatedPackageCreator()
    creator.create_updated_package()
