@echo off
echo 📦 تجميع نظام التراخيص للتوزيع...
echo.

REM التحقق من وجود الملفات المطلوبة
if not exist "dist\برنامج_تفعيل_التراخيص.exe" (
    echo ❌ برنامج التفعيل غير موجود
    echo يرجى تشغيل: pyinstaller --onefile --windowed --name "برنامج_تفعيل_التراخيص" license_activation_app.py
    pause
    exit /b 1
)

if not exist "dist\مولد_أكواد_التفعيل_للمطور.exe" (
    echo ❌ مولد الأكواد غير موجود
    echo يرجى تشغيل: pyinstaller --onefile --windowed --name "مولد_أكواد_التفعيل_للمطور" license_code_generator.py
    pause
    exit /b 1
)

echo ✅ جميع الملفات المطلوبة موجودة
echo.

REM إنشاء مجلدات التوزيع
echo 📁 إنشاء مجلدات التوزيع...

if not exist "dist\نظام_التراخيص_للتوزيع" mkdir "dist\نظام_التراخيص_للتوزيع"
if not exist "dist\نظام_التراخيص_للتوزيع\للعملاء" mkdir "dist\نظام_التراخيص_للتوزيع\للعملاء"
if not exist "dist\نظام_التراخيص_للتوزيع\للمطور" mkdir "dist\نظام_التراخيص_للتوزيع\للمطور"

REM نسخ ملفات العملاء
echo 👥 نسخ ملفات العملاء...
copy "dist\برنامج_تفعيل_التراخيص.exe" "dist\نظام_التراخيص_للتوزيع\للعملاء\"
if exist "تعليمات_برنامج_التفعيل.txt" (
    copy "تعليمات_برنامج_التفعيل.txt" "dist\نظام_التراخيص_للتوزيع\للعملاء\"
)

REM نسخ ملفات المطور
echo 🔧 نسخ ملفات المطور...
copy "dist\مولد_أكواد_التفعيل_للمطور.exe" "dist\نظام_التراخيص_للتوزيع\للمطور\"
if exist "تعليمات_مولد_الأكواد_للمطور.txt" (
    copy "تعليمات_مولد_الأكواد_للمطور.txt" "dist\نظام_التراخيص_للتوزيع\للمطور\"
)

REM نسخ ملف README
if exist "README_نظام_التراخيص.md" (
    copy "README_نظام_التراخيص.md" "dist\نظام_التراخيص_للتوزيع\"
)

echo.
echo ✅ تم تجميع نظام التراخيص بنجاح!
echo.
echo 📁 الملفات متوفرة في:
echo    dist\نظام_التراخيص_للتوزيع\
echo.
echo 👥 للعملاء:
echo    - برنامج_تفعيل_التراخيص.exe
echo    - تعليمات_برنامج_التفعيل.txt
echo.
echo 🔧 للمطور:
echo    - مولد_أكواد_التفعيل_للمطور.exe
echo    - تعليمات_مولد_الأكواد_للمطور.txt
echo.

REM فتح مجلد التوزيع
explorer "dist\نظام_التراخيص_للتوزيع"

pause
