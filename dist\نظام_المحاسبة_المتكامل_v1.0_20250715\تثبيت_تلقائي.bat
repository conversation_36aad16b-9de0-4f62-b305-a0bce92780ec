@echo off
chcp 65001 >nul
title تثبيت نظام المحاسبة المتكامل

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                   🎉 نظام المحاسبة المتكامل                   ║
echo ║                        SICOO Company                         ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.
echo 🚀 مرحباً بك في معالج التثبيت التلقائي
echo.

REM التحقق من الصلاحيات
net session >nul 2>&1
if %errorLevel% == 0 (
    echo ✅ تم تشغيل المعالج بصلاحيات المدير
) else (
    echo ⚠️  يُنصح بتشغيل المعالج كمدير للحصول على أفضل النتائج
)

echo.
echo 📋 التحقق من متطلبات النظام...

REM التحقق من نظام التشغيل
for /f "tokens=4-5 delims=. " %%i in ('ver') do set VERSION=%%i.%%j
echo ✅ نظام التشغيل: Windows %VERSION%

REM التحقق من مساحة القرص
for /f "tokens=3" %%a in ('dir /-c "%SystemDrive%\" ^| find "bytes free"') do set FREE_SPACE=%%a
echo ✅ مساحة القرص المتاحة: %FREE_SPACE% بايت

echo.
echo 📁 اختيار مجلد التثبيت...
set "INSTALL_PATH=%ProgramFiles%\نظام المحاسبة المتكامل"

echo المجلد الافتراضي: %INSTALL_PATH%
echo.
set /p "CUSTOM_PATH=أدخل مجلد مخصص أو اضغط Enter للافتراضي: "

if not "%CUSTOM_PATH%"=="" (
    set "INSTALL_PATH=%CUSTOM_PATH%"
)

echo.
echo 📂 سيتم التثبيت في: %INSTALL_PATH%
echo.

pause
echo.
echo 🔧 بدء عملية التثبيت...

REM إنشاء مجلد التثبيت
if not exist "%INSTALL_PATH%" (
    mkdir "%INSTALL_PATH%" 2>nul
    if errorlevel 1 (
        echo ❌ فشل في إنشاء مجلد التثبيت
        echo يرجى التأكد من الصلاحيات أو اختيار مجلد آخر
        pause
        exit /b 1
    )
    echo ✅ تم إنشاء مجلد التثبيت
)

REM نسخ الملفات
echo 📋 نسخ ملفات النظام...

xcopy /E /I /Y "النظام" "%INSTALL_PATH%\النظام" >nul
if errorlevel 1 (
    echo ❌ فشل في نسخ ملفات النظام
    pause
    exit /b 1
)
echo ✅ تم نسخ ملفات النظام

xcopy /E /I /Y "الأصول" "%INSTALL_PATH%\الأصول" >nul
echo ✅ تم نسخ الأصول

xcopy /E /I /Y "التوثيق" "%INSTALL_PATH%\التوثيق" >nul
echo ✅ تم نسخ التوثيق

xcopy /E /I /Y "الإعدادات" "%INSTALL_PATH%\الإعدادات" >nul
echo ✅ تم نسخ الإعدادات

copy /Y "برنامج_تفعيل_التراخيص.exe" "%INSTALL_PATH%\" >nul
echo ✅ تم نسخ برنامج التفعيل

copy /Y "*.bat" "%INSTALL_PATH%\" >nul
copy /Y "*.txt" "%INSTALL_PATH%\" >nul
copy /Y "*.json" "%INSTALL_PATH%\" >nul
echo ✅ تم نسخ الملفات المساعدة

REM إنشاء اختصارات سطح المكتب
echo 🔗 إنشاء الاختصارات...

REM اختصار النظام الرئيسي
powershell -Command "$WshShell = New-Object -comObject WScript.Shell; $Shortcut = $WshShell.CreateShortcut('%USERPROFILE%\Desktop\نظام المحاسبة.lnk'); $Shortcut.TargetPath = '%INSTALL_PATH%\النظام\نظام المحاسبة.exe'; $Shortcut.WorkingDirectory = '%INSTALL_PATH%\النظام'; $Shortcut.Description = 'نظام المحاسبة المتكامل'; $Shortcut.Save()" 2>nul
if errorlevel 1 (
    echo ⚠️  لم يتم إنشاء اختصار سطح المكتب
) else (
    echo ✅ تم إنشاء اختصار النظام على سطح المكتب
)

REM اختصار برنامج التفعيل
powershell -Command "$WshShell = New-Object -comObject WScript.Shell; $Shortcut = $WshShell.CreateShortcut('%USERPROFILE%\Desktop\تفعيل البرنامج.lnk'); $Shortcut.TargetPath = '%INSTALL_PATH%\برنامج_تفعيل_التراخيص.exe'; $Shortcut.WorkingDirectory = '%INSTALL_PATH%'; $Shortcut.Description = 'تفعيل نظام المحاسبة'; $Shortcut.Save()" 2>nul
if errorlevel 1 (
    echo ⚠️  لم يتم إنشاء اختصار التفعيل
) else (
    echo ✅ تم إنشاء اختصار التفعيل على سطح المكتب
)

REM إنشاء مجلد النسخ الاحتياطية
if not exist "%INSTALL_PATH%\النسخ_الاحتياطية" (
    mkdir "%INSTALL_PATH%\النسخ_الاحتياطية"
    echo ✅ تم إنشاء مجلد النسخ الاحتياطية
)

REM إنشاء ملف إلغاء التثبيت
echo @echo off > "%INSTALL_PATH%\إلغاء_التثبيت.bat"
echo echo 🗑️  إلغاء تثبيت نظام المحاسبة... >> "%INSTALL_PATH%\إلغاء_التثبيت.bat"
echo echo. >> "%INSTALL_PATH%\إلغاء_التثبيت.bat"
echo set /p "CONFIRM=هل أنت متأكد من إلغاء التثبيت؟ (y/N): " >> "%INSTALL_PATH%\إلغاء_التثبيت.bat"
echo if /i "%%CONFIRM%%"=="y" ( >> "%INSTALL_PATH%\إلغاء_التثبيت.bat"
echo     echo 🗑️  حذف الملفات... >> "%INSTALL_PATH%\إلغاء_التثبيت.bat"
echo     cd /d "%%USERPROFILE%%" >> "%INSTALL_PATH%\إلغاء_التثبيت.bat"
echo     rmdir /s /q "%INSTALL_PATH%" >> "%INSTALL_PATH%\إلغاء_التثبيت.bat"
echo     del "%%USERPROFILE%%\Desktop\نظام المحاسبة.lnk" 2^>nul >> "%INSTALL_PATH%\إلغاء_التثبيت.bat"
echo     del "%%USERPROFILE%%\Desktop\تفعيل البرنامج.lnk" 2^>nul >> "%INSTALL_PATH%\إلغاء_التثبيت.bat"
echo     echo ✅ تم إلغاء التثبيت بنجاح >> "%INSTALL_PATH%\إلغاء_التثبيت.bat"
echo ^) else ( >> "%INSTALL_PATH%\إلغاء_التثبيت.bat"
echo     echo ❌ تم إلغاء العملية >> "%INSTALL_PATH%\إلغاء_التثبيت.bat"
echo ^) >> "%INSTALL_PATH%\إلغاء_التثبيت.bat"
echo pause >> "%INSTALL_PATH%\إلغاء_التثبيت.bat"

echo ✅ تم إنشاء ملف إلغاء التثبيت

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                    🎉 تم التثبيت بنجاح!                     ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.
echo 📁 مجلد التثبيت: %INSTALL_PATH%
echo 🖥️  اختصارات سطح المكتب: تم إنشاؤها
echo.
echo 🔑 الخطوة التالية: تفعيل البرنامج
echo    - انقر نقراً مزدوجاً على "تفعيل البرنامج" من سطح المكتب
echo    - أو شغل: %INSTALL_PATH%\برنامج_تفعيل_التراخيص.exe
echo.
echo 📧 للحصول على كود التفعيل أرسل بيانات الجهاز إلى:
echo    <EMAIL>
echo.
echo 📚 للمساعدة راجع: %INSTALL_PATH%\التوثيق\
echo.

set /p "OPEN_ACTIVATION=هل تريد فتح برنامج التفعيل الآن؟ (Y/n): "
if /i "%OPEN_ACTIVATION%"=="n" goto :end

echo.
echo 🔑 فتح برنامج التفعيل...
start "" "%INSTALL_PATH%\برنامج_تفعيل_التراخيص.exe"

:end
echo.
echo 🎉 شكراً لاختيارك نظام المحاسبة المتكامل!
echo.
pause
