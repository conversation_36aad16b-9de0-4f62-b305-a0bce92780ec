@echo off
echo 🔧 بناء مولد أكواد التفعيل للمطور...
echo.

REM التحقق من وجود PyInstaller
python -c "import PyInstaller" 2>nul
if errorlevel 1 (
    echo ❌ PyInstaller غير مثبت. جاري التثبيت...
    pip install pyinstaller
    if errorlevel 1 (
        echo ❌ فشل في تثبيت PyInstaller
        pause
        exit /b 1
    )
)

echo ✅ PyInstaller متوفر
echo.

REM إنشاء مجلد التصدير
if not exist "dist" mkdir dist
if not exist "build" mkdir build

echo 🔨 بناء ملف EXE لمولد الأكواد...
pyinstaller --clean license_generator.spec

if errorlevel 1 (
    echo ❌ فشل في بناء مولد الأكواد
    pause
    exit /b 1
)

echo.
echo ✅ تم بناء مولد الأكواد بنجاح!
echo 📁 الملف موجود في: dist\مولد_أكواد_التفعيل_للمطور.exe
echo.

REM فتح مجلد التصدير
if exist "dist\مولد_أكواد_التفعيل_للمطور.exe" (
    echo 🎉 تم إنشاء مولد الأكواد بنجاح!
    explorer dist
) else (
    echo ❌ لم يتم العثور على الملف المُصدَّر
)

pause
